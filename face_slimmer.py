import cv2
import dlib
import numpy as np
from scipy.spatial import Delaunay

# --- Initialize Models ---
detector = dlib.get_frontal_face_detector()

# Load shape predictor (ensure the .dat file is in the same folder)
try:
    predictor = dlib.shape_predictor("shape_predictor_68_face_landmarks.dat")
except Exception as e:
    raise FileNotFoundError(f"❌ shape_predictor_68_face_landmarks.dat not found. Download it from: http://dlib.net/files/shape_predictor_68_face_landmarks.dat.bz2\nError: {e}")

# --- Helper Functions ---
def shape_to_np(shape):
    """Convert dlib shape object to numpy array of (68,2) landmarks"""
    coords = np.zeros((68, 2), dtype="int")
    for i in range(68):
        coords[i] = (shape.part(i).x, shape.part(i).y)
    return coords

def thin_plate_spline_warp(image, src_points, dst_points):
    """Warp image using Delaunay triangulation and affine transforms"""
    h, w = image.shape[:2]
    warped = np.zeros_like(image)
    
    # Triangulate source points
    try:
        tri = Delaunay(src_points)
    except:
        print("⚠️ Delaunay triangulation failed. Check landmark points.")
        return image

    for simplex in tri.simplices:
        if len(simplex) != 3:  # Skip non-triangles
            continue

        src_tri = np.float32([src_points[i] for i in simplex])
        dst_tri = np.float32([dst_points[i] for i in simplex])

        # Get bounding rectangles
        src_rect = cv2.boundingRect(src_tri)
        dst_rect = cv2.boundingRect(dst_tri)

        # Offset points to ROI
        src_offset = src_tri - src_rect[:2]
        dst_offset = dst_tri - dst_rect[:2]

        # Validate before affine transform
        if src_offset.shape == (3, 2) and dst_offset.shape == (3, 2):
            try:
                M = cv2.getAffineTransform(src_offset, dst_offset)
                src_cropped = image[src_rect[1]:src_rect[1]+src_rect[3], 
                                   src_rect[0]:src_rect[0]+src_rect[2]]
                
                # Warp the triangle
                warped_cropped = cv2.warpAffine(
                    src_cropped, M, (dst_rect[2], dst_rect[3]),
                    flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_REFLECT_101
                )

                # Create mask and blend
                mask = np.zeros((dst_rect[3], dst_rect[2], 3), dtype=np.float32)
                cv2.fillConvexPoly(mask, np.int32(dst_offset), (1, 1, 1), 16, 0)
                
                # Composite the warped triangle
                warped[dst_rect[1]:dst_rect[1]+dst_rect[3],
                       dst_rect[0]:dst_rect[0]+dst_rect[2]] = \
                    warped[dst_rect[1]:dst_rect[1]+dst_rect[3],
                           dst_rect[0]:dst_rect[0]+dst_rect[2]] * (1 - mask) + warped_cropped * mask
            except cv2.error as e:
                print(f"⚠️ Skipping invalid triangle: {e}")

    return warped

def slim_face(image, landmarks, ratio=0.9):
    """Slim face by moving jawline and cheek landmarks inward"""
    img = image.copy()
    
    # Landmark indices for jawline and cheeks
    jaw_indices = list(range(0, 17))  # Chin to jaw
    cheek_indices = [3, 4, 5, 11, 12, 13]  # Inner cheeks
    selected_indices = jaw_indices + cheek_indices

    src_points = landmarks.copy()
    dst_points = landmarks.copy()

    # Move points inward relative to face center
    face_center_x = np.mean(landmarks[:, 0])
    for i in selected_indices:
        dst_points[i, 0] = face_center_x + (landmarks[i, 0] - face_center_x) * ratio

    return thin_plate_spline_warp(img, src_points, dst_points)

# --- Main Pipeline ---
def main():
    # Load image
    image = cv2.imread("face.jpg")
    if image is None:
        raise FileNotFoundError("❌ 'face.jpg' not found in the current directory.")

    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Detect faces
    faces = detector(gray)
    if len(faces) == 0:
        raise ValueError("❌ No faces detected in the image.")

    for face in faces:
        shape = predictor(gray, face)
        landmarks = shape_to_np(shape)
        print(f"✅ Detected {len(landmarks)} facial landmarks.")

        # Slim face (adjust ratio: 0.8 = more aggressive, 1.0 = no change)
        slimmed = slim_face(image, landmarks, ratio=0.88)  

        # Save result
        cv2.imwrite("face_slimmed.jpg", slimmed)
        print("🎉 Face slimming complete → face_slimmed.jpg")

if __name__ == "__main__":
    main()